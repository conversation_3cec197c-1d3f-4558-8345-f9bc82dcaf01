// ==UserScript==
// @name         AugmentCode自动注册
// @namespace    http://tampermonkey.net/
// @version      0.3
// @description  自动完成AugmentCode的注册流程
// <AUTHOR> JOU
// @match        https://*.augmentcode.com/*
// @match        https://www.augmentcode.com/resources/windsurf
// @icon         https://www.google.com/s2/favicons?sz=64&domain=augmentcode.com
// @grant        GM_xmlhttpRequest
// @grant        GM_setValue
// @grant        GM_getValue
// @grant        GM_log
// @grant        GM_setClipboard
// @connect      tempmail.plus
// ==/UserScript==

(function() {
    'use strict';

    // 创建日志UI
    function createLogUI() {
        const logContainer = document.createElement('div');
        logContainer.innerHTML = `
            <div id="auto-register-log" style="
                position: fixed;
                bottom: 40px;
                right: 20px;
                width: 300px;
                max-height: 400px;
                background: rgba(255, 255, 255, 0.95);
                border-radius: 8px;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
                z-index: 10000;
                font-family: Arial, sans-serif;
                overflow: hidden;
                display: flex;
                flex-direction: column;
            ">
                <div style="
                    padding: 12px;
                    background: #1a73e8;
                    color: white;
                    font-weight: bold;
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                ">
                    <span>自动注册日志</span>
                    <div>
                        <button id="auto-register-btn" style="
                            background: #34a853;
                            border: none;
                            color: white;
                            cursor: pointer;
                            font-size: 12px;
                            padding: 4px 8px;
                            border-radius: 4px;
                            margin-right: 8px;
                            display: none;
                        ">开始注册</button>
                        <button id="clear-log" style="
                            background: transparent;
                            border: none;
                            color: white;
                            cursor: pointer;
                            font-size: 12px;
                            padding: 4px 8px;
                            border-radius: 4px;
                        ">清除</button>
                        <button id="minimize-log" style="
                            background: transparent;
                            border: none;
                            color: white;
                            cursor: pointer;
                            font-size: 14px;
                            padding: 4px 8px;
                            margin-left: 8px;
                        ">_</button>
                    </div>
                </div>
                <div style="
                    padding: 8px 12px;
                    background: #f8f9fa;
                    border-bottom: 1px solid #e8eaed;
                    font-size: 12px;
                    color: #5f6368;
                    display: flex;
                    align-items: center;
                    gap: 8px;
                ">
                    <span style="color: #1a73e8;">📢</span>
                    <span>NEO JOU</span>
                </div>
                <div id="log-content" style="
                    padding: 12px;
                    overflow-y: auto;
                    max-height: 300px;
                    font-size: 13px;
                "></div>
            </div>
        `;

        document.body.appendChild(logContainer);

        // 最小化功能
        let isMinimized = false;
        const logContent = document.getElementById('log-content');
        const minimizeBtn = document.getElementById('minimize-log');

        minimizeBtn.addEventListener('click', () => {
            isMinimized = !isMinimized;
            logContent.style.display = isMinimized ? 'none' : 'block';
            minimizeBtn.textContent = isMinimized ? '□' : '_';
        });

        // 清除日志功能
        const clearBtn = document.getElementById('clear-log');
        clearBtn.addEventListener('click', () => {
            logContent.innerHTML = '';
        });

        return {
            log: function(message, type = 'info') {
                const logEntry = document.createElement('div');
                logEntry.style.marginBottom = '8px';
                logEntry.style.padding = '8px';
                logEntry.style.borderRadius = '4px';
                logEntry.style.wordBreak = 'break-word';

                switch(type) {
                    case 'success':
                        logEntry.style.background = '#e6f4ea';
                        logEntry.style.color = '#1e8e3e';
                        break;
                    case 'error':
                        logEntry.style.background = '#fce8e6';
                        logEntry.style.color = '#d93025';
                        break;
                    case 'warning':
                        logEntry.style.background = '#fef7e0';
                        logEntry.style.color = '#ea8600';
                        break;
                    default:
                        logEntry.style.background = '#f8f9fa';
                        logEntry.style.color = '#202124';
                }

                const time = new Date().toLocaleTimeString();
                logEntry.textContent = `[${time}] ${message}`;
                logContent.appendChild(logEntry);
                logContent.scrollTop = logContent.scrollHeight;
            },
            showRegisterButton: function() {
                const registerBtn = document.getElementById('auto-register-btn');
                if (registerBtn) {
                    logger.log('找到注册按钮，正在显示...');
                    registerBtn.style.display = 'inline-block';
                    return registerBtn;
                } else {
                    logger.log('未找到注册按钮元素', 'error');
                    return null;
                }
            }
        };
    }

    // 创建全局日志对象
    const logger = createLogUI();

    // 配置
    const EMAIL_DOMAINS = [
        '@neoz.ltd',
        '@adidas.neoz.ltd',
        '@balen.neoz.ltd',
        '@dahua.neoz.ltd',
        '@tita.neoz.ltd',
        '@hot.neoz.ltd',
        '@tian1.neoz.ltd',
        '@apple.neoz.ltd',
        '@xiaoshuo.neoz.ltd',
        '@zhou1.shop',
        '@886.zhou1.shop',
        '@ai.zhou1.shop',
        '@tencent.zhou1.shop',
        '@xyz.zhou1.shop',
        '@feng.zhou1.shop',
        '@huawei.zhou1.shop',
        '@bmw.zhou1.shop',
        '@china.zhou1.shop'
    ];
    const TEMP_MAIL_CONFIG = {
        username: "neo888",
        emailExtension: "@mailto.plus",
        epin: "0418"
    };

    const FIRST_NAMES = ["linda", "john", "mary", "david", "sarah", "michael", "jennifer"];
    const LAST_NAMES = ["garcia", "smith", "johnson", "brown", "davis", "miller", "wilson"];

    // 生成随机邮箱
    function generateEmail() {
        const firstName = FIRST_NAMES[Math.floor(Math.random() * FIRST_NAMES.length)];
        const lastName = LAST_NAMES[Math.floor(Math.random() * LAST_NAMES.length)];
        const randomDomain = EMAIL_DOMAINS[Math.floor(Math.random() * EMAIL_DOMAINS.length)];
        const separators = ['.', '_', ''];
        const separator = separators[Math.floor(Math.random() * separators.length)];
    
        let username;
        const style = Math.floor(Math.random() * 5); // 5种不同的风格
    
        switch (style) {
            case 0: // firstname.lastname + random numbers
                username = `${firstName}${separator}${lastName}${Math.floor(Math.random() * 1000)}`;
                break;
            case 1: // lastname + firstname + year
                username = `${lastName}${firstName}${1980 + Math.floor(Math.random() * 25)}`;
                break;
            case 2: // initial + lastname + random numbers
                username = `${firstName.charAt(0)}${lastName}${Math.floor(Math.random() * 100)}`;
                break;
            case 3: // firstname + random numbers
                username = `${firstName}${Math.floor(Math.random() * 9000) + 1000}`;
                break;
            case 4: // creative combination
                const randomPart = Math.random().toString(36).substring(2, 7);
                username = `${firstName}${separator}${randomPart}`;
                break;
            default: // fallback to original-like but simpler
                 username = `${firstName}${lastName}${Math.floor(Math.random() * 100)}`;
                 break;
        }
    
        return `${username.toLowerCase()}${randomDomain}`;
    }

    // 等待元素出现
    async function waitForElement(selector, timeout = 10000, doc = document) {
        const startTime = Date.now();
        while (Date.now() - startTime < timeout) {
            const element = doc.querySelector(selector);
            if (element) {
                return element;
            }
            await new Promise(resolve => setTimeout(resolve, 100));
        }
        return null;
    }

    // 从邮件文本中提取验证码
    function extractVerificationCode(mailText) {
        const codeMatch = mailText.match(/(?<![a-zA-Z@.])\b\d{6}\b/);
        return codeMatch ? codeMatch[0] : null;
    }

    // 删除邮件
    async function deleteEmail(firstId) {
        return new Promise((resolve, reject) => {
            const deleteUrl = 'https://tempmail.plus/api/mails/';
            const maxRetries = 5;
            let retryCount = 0;

            function tryDelete() {
                GM_xmlhttpRequest({
                    method: "DELETE",
                    url: deleteUrl,
                    data: `email=${TEMP_MAIL_CONFIG.username}${TEMP_MAIL_CONFIG.emailExtension}&first_id=${firstId}&epin=${TEMP_MAIL_CONFIG.epin}`,
                    headers: {
                        "Content-Type": "application/x-www-form-urlencoded"
                    },
                    onload: function(response) {
                        try {
                            const result = JSON.parse(response.responseText).result;
                            if (result === true) {
                                logger.log("邮件删除成功", 'success');
                                resolve(true);
                                return;
                            }
                        } catch (error) {
                            logger.log("解析删除响应失败: " + error, 'warning');
                        }

                        // 如果还有重试次数，继续尝试
                        if (retryCount < maxRetries - 1) {
                            retryCount++;
                            logger.log(`删除邮件失败，正在重试 (${retryCount}/${maxRetries})...`, 'warning');
                            setTimeout(tryDelete, 500);
                        } else {
                            logger.log("删除邮件失败，已达到最大重试次数", 'error');
                            resolve(false);
                        }
                    },
                    onerror: function(error) {
                        if (retryCount < maxRetries - 1) {
                            retryCount++;
                            logger.log(`删除邮件出错，正在重试 (${retryCount}/${maxRetries})...`, 'warning');
                            setTimeout(tryDelete, 500);
                        } else {
                            logger.log("删除邮件失败: " + error, 'error');
                            resolve(false);
                        }
                    }
                });
            }

            tryDelete();
        });
    }

    // 获取最新邮件中的验证码
    async function getLatestMailCode() {
        return new Promise((resolve, reject) => {
            const mailListUrl = `https://tempmail.plus/api/mails?email=${TEMP_MAIL_CONFIG.username}${TEMP_MAIL_CONFIG.emailExtension}&limit=20&epin=${TEMP_MAIL_CONFIG.epin}`;

            GM_xmlhttpRequest({
                method: "GET",
                url: mailListUrl,
                onload: async function(mailListResponse) {
                    try {
                        const mailListData = JSON.parse(mailListResponse.responseText);
                        if (!mailListData.result || !mailListData.first_id) {
                            resolve(null);
                            return;
                        }

                        const firstId = mailListData.first_id;
                        const mailDetailUrl = `https://tempmail.plus/api/mails/${firstId}?email=${TEMP_MAIL_CONFIG.username}${TEMP_MAIL_CONFIG.emailExtension}&epin=${TEMP_MAIL_CONFIG.epin}`;

                        GM_xmlhttpRequest({
                            method: "GET",
                            url: mailDetailUrl,
                            onload: async function(mailDetailResponse) {
                                try {
                                    const mailDetailData = JSON.parse(mailDetailResponse.responseText);
                                    if (!mailDetailData.result) {
                                        resolve(null);
                                        return;
                                    }

                                    const mailText = mailDetailData.text || "";
                                    const mailSubject = mailDetailData.subject || "";
                                    logger.log("找到邮件主题: " + mailSubject);

                                    const code = extractVerificationCode(mailText);

                                    // 获取到验证码后，尝试删除邮件
                                    if (code) {
                                        await deleteEmail(firstId);
                                    }

                                    resolve(code);
                                } catch (error) {
                                    logger.log("解析邮件详情失败: " + error, 'error');
                                    resolve(null);
                                }
                            },
                            onerror: function(error) {
                                logger.log("获取邮件详情失败: " + error, 'error');
                                resolve(null);
                            }
                        });
                    } catch (error) {
                        logger.log("解析邮件列表失败: " + error, 'error');
                        resolve(null);
                    }
                },
                onerror: function(error) {
                    logger.log("获取邮件列表失败: " + error, 'error');
                    resolve(null);
                }
            });
        });
    }

    // 获取验证码（带重试机制）
    async function getVerificationCode(maxRetries = 5, retryInterval = 3000) {
        for (let attempt = 0; attempt < maxRetries; attempt++) {
            logger.log(`尝试获取验证码 (第 ${attempt + 1}/${maxRetries} 次)...`);

            try {
                const code = await getLatestMailCode();
                if (code) {
                    logger.log("成功获取验证码: " + code, 'success');
                    return code;
                }

                if (attempt < maxRetries - 1) {
                    logger.log(`未获取到验证码，${retryInterval/1000}秒后重试...`, 'warning');
                    await new Promise(resolve => setTimeout(resolve, retryInterval));
                }
            } catch (error) {
                logger.log("获取验证码出错: " + error, 'error');
                if (attempt < maxRetries - 1) {
                    await new Promise(resolve => setTimeout(resolve, retryInterval));
                }
            }
        }

        throw new Error(`经过 ${maxRetries} 次尝试后仍未获取到验证码。`);
    }

    // 自动填写邮箱并提交
    async function fillEmail() {
        const email = generateEmail();
        logger.log('使用邮箱: ' + email);
        GM_setValue('registered_email', email); // 保存邮箱以便后续使用

        const emailInput = await waitForElement('input[name="username"]');
        if (!emailInput) {
            logger.log('未找到邮箱输入框', 'error');
            return false;
        }

        logger.log('找到邮箱输入框，开始填写');

        // 填写邮箱
        emailInput.value = email;
        emailInput.dispatchEvent(new Event('input', { bubbles: true }));

        // 点击继续按钮
        const continueBtn = await waitForElement('button[type="submit"]');
        if (!continueBtn) {
            logger.log('未找到继续按钮', 'error');
            return false;
        }

        continueBtn.click();
        return true;
    }

    // 填写验证码
    async function fillVerificationCode() {
        const code = await getVerificationCode();
        if (!code) {
            logger.log('未能获取验证码', 'error');
            return false;
        }

        const codeInput = await waitForElement('input[name="code"]');
        if (!codeInput) {
            logger.log('未找到验证码输入框', 'error');
            return false;
        }

        // 填写验证码
        codeInput.value = code;
        codeInput.dispatchEvent(new Event('input', { bubbles: true }));

        // 点击继续按钮
        const continueBtn = await waitForElement('button[type="submit"]');
        if (!continueBtn) {
            logger.log('未找到继续按钮', 'error');
            return false;
        }

        continueBtn.click();
        return true;
    }

    // 同意服务条款并完成注册
    async function completeRegistration() {
        const checkbox = await waitForElement('input[type="checkbox"]');
        if (checkbox) {
            checkbox.click();
        }

        const signupBtn = await waitForElementWithText('button', 'Sign up');
        if (!signupBtn) {
            logger.log('未找到注册按钮', 'error');
            return false;
        }

        signupBtn.click();
        return true;
    }

    // 复制到剪贴板
    function copyToClipboard(text) {
        GM_setClipboard(text, 'text');
        logger.log(`已复制到剪贴板: ${text}`, 'success');
    }

    // 等待包含特定文本的元素
    async function waitForElementWithText(selector, text, timeout = 10000, doc = document) {
        const startTime = Date.now();
        while (Date.now() - startTime < timeout) {
            const elements = doc.querySelectorAll(selector);
            for (const element of elements) {
                const elementText = element.textContent.replace(/\s+/g, ' ').trim();
                const normalizedText = text.replace(/\s+/g, ' ').trim();
                if (elementText.includes(normalizedText)) {
                    return element;
                }
            }
            await new Promise(resolve => setTimeout(resolve, 100));
        }
        return null;
    }

    async function handleWindsurfPage() {
        const getCreditsBtn = await waitForElementWithText('a', 'Get free credits');
        if (getCreditsBtn) {
            logger.log('找到 "Get free credits" 按钮，正在点击...');
            getCreditsBtn.click();
        } else {
            logger.log('未找到 "Get free credits" 按钮', 'error');
        }
    }

    // Function to find an element, including searching inside Shadow DOMs
    function findInShadowDom(selector, text) {
        const find = (root) => {
            // Search in the current root
            const elements = root.querySelectorAll(selector);
            for (const element of elements) {
                if (element.textContent.includes(text)) {
                    return element;
                }
            }
            // Search in shadow roots
            const shadowHosts = root.querySelectorAll('*');
            for (const host of shadowHosts) {
                if (host.shadowRoot) {
                    const found = find(host.shadowRoot);
                    if (found) return found;
                }
            }
            return null;
        };
        return find(document.body);
    }

    async function handleHumanVerification() {
        logger.log('开始人机验证...');
        const maxRetries = 5;
        const retryInterval = 3000; // 3 seconds

        for (let attempt = 1; attempt <= maxRetries; attempt++) {
            logger.log(`人机验证尝试 #${attempt}/${maxRetries}...`);

            const textElement = findInShadowDom('*', 'Verify you are human');

            if (textElement) {
                // Once the text is found, find the checkbox within its root
                const root = textElement.getRootNode();
                const checkbox = root.querySelector('input[type="checkbox"]');

                if (checkbox && !checkbox.checked) {
                    logger.log('找到验证复选框，正在点击...');
                    checkbox.click();

                    logger.log('等待验证成功...');
                    const successElement = await waitForElementWithText('*', 'Success!', 15000);
                    if (successElement) {
                        logger.log('人机验证成功！', 'success');
                        return; // Success, exit the function
                    } else {
                        logger.log('点击后验证失败或超时。', 'error');
                        throw new Error('Verification failed after clicking checkbox.');
                    }
                }
            }

            if (attempt < maxRetries) {
                logger.log('未找到验证元素，稍后重试...', 'warning');
                await new Promise(resolve => setTimeout(resolve, retryInterval));
            }
        }

        logger.log(`在 ${maxRetries} 次尝试后，仍未找到可操作的人机验证元素。`, 'error');
        throw new Error('Could not find the human verification element after multiple retries.');
    }

    async function handleSubscriptionPage() {
        if (document.body.innerText.includes('650 available')) {
            logger.log('已成功切换到Community Plan。', 'success');
            const email = await GM_getValue('registered_email', '');
            if (email) {
                copyToClipboard(email);
            }
            return;
        }

        logger.log('开始切换订阅计划...');
        const changePlanBtn = await waitForElementWithText('button', 'Change plan');
        if (!changePlanBtn) {
            logger.log('未找到 "Change plan" 按钮', 'error');
            return;
        }
        changePlanBtn.click();
        logger.log('点击 "Change plan" 按钮');

        const communityPlanBtn = await waitForElementWithText('h3', 'Community Plan', 5000);
        if (!communityPlanBtn) {
            logger.log('未找到 "Community Plan"', 'error');
            return;
        }
        communityPlanBtn.parentElement.click();
        logger.log('选择 "Community Plan"');

        const confirmCheckbox = await waitForElement('input[type="checkbox"]', 5000);
        if (!confirmCheckbox) {
            logger.log('未找到确认复选框', 'error');
            return;
        }
        if (!confirmCheckbox.checked) {
            confirmCheckbox.click();
            logger.log('勾选确认框');
        }

        const selectPlanBtn = await waitForElementWithText('button', 'Select Plan');
        if (!selectPlanBtn) {
            logger.log('未找到 "Select Plan" 按钮', 'error');
            return;
        }
        selectPlanBtn.click();
        logger.log('点击 "Select Plan" 按钮');

        logger.log('等待计划切换成功...');
        const successIndicator = await waitForElementWithText('div', '650 available', 20000);
        if (successIndicator) {
            logger.log('成功切换到Community Plan！', 'success');
            const email = await GM_getValue('registered_email', '');
            if (email) {
                copyToClipboard(email);
            }
        } else {
            logger.log('切换计划失败或超时。', 'error');
        }
    }

    async function handleLoginPage() {
        const emailInput = document.querySelector('input[name="username"]');
        const codeInput = document.querySelector('input[name="code"]');
        const termsCheckbox = document.querySelector('input[type="checkbox"]'); // More generic selector
        const isFailed = document.body.innerText.includes('Sign up failed');

        if (isFailed) {
            logger.log('注册失败！服务器拒绝了请求。这通常是因为临时邮箱域名被屏蔽或IP地址被标记。', 'error');
            logger.log('建议：请尝试在 tempmail.plus 网站更换一个新的邮箱域名，然后修改脚本中的 EMAIL_DOMAIN。', 'warning');
        } else if (emailInput) {
            logger.log('检测到邮箱输入页面');
            await handleHumanVerification();
            const registerButton = logger.showRegisterButton();
            if (registerButton) {
                registerButton.addEventListener('click', async () => {
                    try {
                        registerButton.disabled = true;
                        registerButton.textContent = '正在填写...';
                        if (await fillEmail()) {
                            logger.log('邮箱填写完成，请等待页面跳转...', 'success');
                        }
                    } catch (error) {
                        logger.log('填写邮箱过程出错: ' + error, 'error');
                        registerButton.disabled = false;
                        registerButton.textContent = '重试';
                    }
                });
            }
        } else if (codeInput) {
            logger.log('检测到验证码输入页面，自动执行...');
            try {
                if (await fillVerificationCode()) {
                    logger.log('验证码填写完成，继续注册...', 'success');
                }
            } catch (error) {
                logger.log('填写验证码过程出错: ' + error, 'error');
            }
        } else if (termsCheckbox && document.body.innerText.includes('Terms of Service')) {
            logger.log('检测到服务条款页面，自动完成...');
            try {
                if (await completeRegistration()) {
                    logger.log('注册流程完成！等待跳转...', 'success');
                }
            } catch (error) {
                logger.log('完成注册过程出错: ' + error, 'error');
            }
        } else {
            logger.log('无法识别当前登录页面状态', 'warning');
        }
    }

    // 主函数
    async function main() {
        const currentUrl = window.location.href;
        logger.log(`当前URL: ${currentUrl}`);

        if (currentUrl.includes('/resources/windsurf')) {
            logger.log('检测到Windsurf页面');
            await handleWindsurfPage();
        } else if (currentUrl.includes('login.augmentcode.com') || currentUrl.includes('auth.augmentcode.com')) {
            logger.log('检测到注册/登录页面');
            await handleLoginPage();
        } else if (currentUrl.includes('/promotions/windsurf')) {
            logger.log('检测到文件上传页面，请手动上传PDF文件以继续。', 'warning');
            // The script will wait for navigation to the subscription page.
        } else if (currentUrl.includes('/account/subscription')) {
            logger.log('检测到Subscription页面');
            await handleSubscriptionPage();
        } else {
            logger.log('当前页面不是脚本目标页面', 'info');
        }
    }

    // 启动脚本
    main().catch(error => logger.log('脚本启动出错: ' + error, 'error'));
})();
