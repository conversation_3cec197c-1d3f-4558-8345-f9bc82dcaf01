// ==UserScript==
// @name         AugmentCode自动注册
// @namespace    http://tampermonkey.net/
// @version      0.3
// @description  自动完成AugmentCode的注册流程
// <AUTHOR> JOU
// @match        https://*.augmentcode.com/*
// @match        https://www.augmentcode.com/resources/windsurf
// @icon         https://www.google.com/s2/favicons?sz=64&domain=augmentcode.com
// @grant        GM_xmlhttpRequest
// @grant        GM_setValue
// @grant        GM_getValue
// @grant        GM_log
// @grant        GM_setClipboard
// @connect      tempmail.plus
// ==/UserScript==

(function() {
    'use strict';

    // 创建日志UI
    function createLogUI() {
        const logContainer = document.createElement('div');
        logContainer.innerHTML = `
            <div id="auto-register-log" style="
                position: fixed;
                bottom: 40px;
                right: 20px;
                width: 300px;
                max-height: 400px;
                background: rgba(255, 255, 255, 0.95);
                border-radius: 8px;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
                z-index: 10000;
                font-family: Arial, sans-serif;
                overflow: hidden;
                display: flex;
                flex-direction: column;
            ">
                <div style="
                    padding: 12px;
                    background: #1a73e8;
                    color: white;
                    font-weight: bold;
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                ">
                    <span>自动注册日志</span>
                    <div>
                        <button id="auto-register-btn" style="
                            background: #34a853;
                            border: none;
                            color: white;
                            cursor: pointer;
                            font-size: 12px;
                            padding: 4px 8px;
                            border-radius: 4px;
                            margin-right: 8px;
                            display: none;
                        ">开始注册</button>
                        <button id="clear-log" style="
                            background: transparent;
                            border: none;
                            color: white;
                            cursor: pointer;
                            font-size: 12px;
                            padding: 4px 8px;
                            border-radius: 4px;
                        ">清除</button>
                        <button id="minimize-log" style="
                            background: transparent;
                            border: none;
                            color: white;
                            cursor: pointer;
                            font-size: 14px;
                            padding: 4px 8px;
                            margin-left: 8px;
                        ">_</button>
                    </div>
                </div>
                <div style="
                    padding: 8px 12px;
                    background: #f8f9fa;
                    border-bottom: 1px solid #e8eaed;
                    font-size: 12px;
                    color: #5f6368;
                    display: flex;
                    align-items: center;
                    gap: 8px;
                ">
                    <span style="color: #1a73e8;">📢</span>
                    <span>NEO JOU</span>
                </div>
                <div id="log-content" style="
                    padding: 12px;
                    overflow-y: auto;
                    max-height: 300px;
                    font-size: 13px;
                "></div>
            </div>
        `;

        document.body.appendChild(logContainer);

        // 最小化功能
        let isMinimized = false;
        const logContent = document.getElementById('log-content');
        const minimizeBtn = document.getElementById('minimize-log');

        minimizeBtn.addEventListener('click', () => {
            isMinimized = !isMinimized;
            logContent.style.display = isMinimized ? 'none' : 'block';
            minimizeBtn.textContent = isMinimized ? '□' : '_';
        });

        // 清除日志功能
        const clearBtn = document.getElementById('clear-log');
        clearBtn.addEventListener('click', () => {
            logContent.innerHTML = '';
        });

        return {
            log: function(message, type = 'info') {
                const logEntry = document.createElement('div');
                logEntry.style.marginBottom = '8px';
                logEntry.style.padding = '8px';
                logEntry.style.borderRadius = '4px';
                logEntry.style.wordBreak = 'break-word';

                switch(type) {
                    case 'success':
                        logEntry.style.background = '#e6f4ea';
                        logEntry.style.color = '#1e8e3e';
                        break;
                    case 'error':
                        logEntry.style.background = '#fce8e6';
                        logEntry.style.color = '#d93025';
                        break;
                    case 'warning':
                        logEntry.style.background = '#fef7e0';
                        logEntry.style.color = '#ea8600';
                        break;
                    default:
                        logEntry.style.background = '#f8f9fa';
                        logEntry.style.color = '#202124';
                }

                const time = new Date().toLocaleTimeString();
                logEntry.textContent = `[${time}] ${message}`;
                logContent.appendChild(logEntry);
                logContent.scrollTop = logContent.scrollHeight;
            },
            showRegisterButton: function() {
                const registerBtn = document.getElementById('auto-register-btn');
                if (registerBtn) {
                    logger.log('找到注册按钮，正在显示...');
                    registerBtn.style.display = 'inline-block';
                    return registerBtn;
                } else {
                    logger.log('未找到注册按钮元素', 'error');
                    return null;
                }
            }
        };
    }

    // 创建全局日志对象
    const logger = createLogUI();

    // 配置
    const EMAIL_DOMAINS = [
        '@neoz.ltd',
        '@adidas.neoz.ltd',
        '@balen.neoz.ltd',
        '@dahua.neoz.ltd',
        '@tita.neoz.ltd',
        '@hot.neoz.ltd',
        '@tian1.neoz.ltd',
        '@apple.neoz.ltd',
        '@xiaoshuo.neoz.ltd',
        '@zhou1.shop',
        '@886.zhou1.shop',
        '@ai.zhou1.shop',
        '@tencent.zhou1.shop',
        '@xyz.zhou1.shop',
        '@feng.zhou1.shop',
        '@huawei.zhou1.shop',
        '@bmw.zhou1.shop',
        '@china.zhou1.shop'
    ];
    const TEMP_MAIL_CONFIG = {
        username: "neo888",
        emailExtension: "@mailto.plus",
        epin: "0418"
    };

    const FIRST_NAMES = ["linda", "john", "mary", "david", "sarah", "michael", "jennifer"];
    const LAST_NAMES = ["garcia", "smith", "johnson", "brown", "davis", "miller", "wilson"];

    // 生成随机邮箱
    function generateEmail() {
        const firstName = FIRST_NAMES[Math.floor(Math.random() * FIRST_NAMES.length)];
        const lastName = LAST_NAMES[Math.floor(Math.random() * LAST_NAMES.length)];
        const randomDomain = EMAIL_DOMAINS[Math.floor(Math.random() * EMAIL_DOMAINS.length)];
        const separators = ['.', '_', ''];
        const separator = separators[Math.floor(Math.random() * separators.length)];
    
        let username;
        const style = Math.floor(Math.random() * 5); // 5种不同的风格
    
        switch (style) {
            case 0: // firstname.lastname + random numbers
                username = `${firstName}${separator}${lastName}${Math.floor(Math.random() * 1000)}`;
                break;
            case 1: // lastname + firstname + year
                username = `${lastName}${firstName}${1980 + Math.floor(Math.random() * 25)}`;
                break;
            case 2: // initial + lastname + random numbers
                username = `${firstName.charAt(0)}${lastName}${Math.floor(Math.random() * 100)}`;
                break;
            case 3: // firstname + random numbers
                username = `${firstName}${Math.floor(Math.random() * 9000) + 1000}`;
                break;
            case 4: // creative combination
                const randomPart = Math.random().toString(36).substring(2, 7);
                username = `${firstName}${separator}${randomPart}`;
                break;
            default: // fallback to original-like but simpler
                 username = `${firstName}${lastName}${Math.floor(Math.random() * 100)}`;
                 break;
        }
    
        return `${username.toLowerCase()}${randomDomain}`;
    }

    // 等待元素出现
    async function waitForElement(selector, timeout = 10000, doc = document) {
        const startTime = Date.now();
        while (Date.now() - startTime < timeout) {
            const element = doc.querySelector(selector);
            if (element) {
                return element;
            }
            await new Promise(resolve => setTimeout(resolve, 100));
        }
        return null;
    }

    // 检查元素是否可见且可交互
    function isElementVisible(element) {
        if (!element) return false;

        const rect = element.getBoundingClientRect();
        const style = window.getComputedStyle(element);

        return (
            rect.width > 0 &&
            rect.height > 0 &&
            style.visibility !== 'hidden' &&
            style.display !== 'none' &&
            style.opacity !== '0' &&
            rect.top >= 0 &&
            rect.left >= 0 &&
            rect.bottom <= (window.innerHeight || document.documentElement.clientHeight) &&
            rect.right <= (window.innerWidth || document.documentElement.clientWidth)
        );
    }

    // 智能等待元素（支持多种定位策略）
    async function waitForElementAdvanced(strategies, timeout = 15000) {
        const startTime = Date.now();
        let attempt = 0;

        while (Date.now() - startTime < timeout) {
            attempt++;

            for (let i = 0; i < strategies.length; i++) {
                const strategy = strategies[i];
                logger.log(`尝试策略 ${i + 1}/${strategies.length}: ${strategy.name} (第${attempt}次)`);

                try {
                    const element = await strategy.find();
                    if (element && isElementVisible(element)) {
                        logger.log(`策略 "${strategy.name}" 成功找到可见元素`, 'success');
                        return { element, strategy: strategy.name };
                    } else if (element) {
                        logger.log(`策略 "${strategy.name}" 找到元素但不可见`, 'warning');
                    }
                } catch (error) {
                    logger.log(`策略 "${strategy.name}" 执行出错: ${error.message}`, 'warning');
                }
            }

            // 指数退避等待
            const waitTime = Math.min(200 * Math.pow(1.5, Math.min(attempt, 8)), 2000);
            await new Promise(resolve => setTimeout(resolve, waitTime));
        }

        logger.log('所有策略均未找到可用元素', 'error');
        return null;
    }

    // 从邮件文本中提取验证码
    function extractVerificationCode(mailText) {
        const codeMatch = mailText.match(/(?<![a-zA-Z@.])\b\d{6}\b/);
        return codeMatch ? codeMatch[0] : null;
    }

    // 删除邮件
    async function deleteEmail(firstId) {
        return new Promise((resolve, reject) => {
            const deleteUrl = 'https://tempmail.plus/api/mails/';
            const maxRetries = 5;
            let retryCount = 0;

            function tryDelete() {
                GM_xmlhttpRequest({
                    method: "DELETE",
                    url: deleteUrl,
                    data: `email=${TEMP_MAIL_CONFIG.username}${TEMP_MAIL_CONFIG.emailExtension}&first_id=${firstId}&epin=${TEMP_MAIL_CONFIG.epin}`,
                    headers: {
                        "Content-Type": "application/x-www-form-urlencoded"
                    },
                    onload: function(response) {
                        try {
                            const result = JSON.parse(response.responseText).result;
                            if (result === true) {
                                logger.log("邮件删除成功", 'success');
                                resolve(true);
                                return;
                            }
                        } catch (error) {
                            logger.log("解析删除响应失败: " + error, 'warning');
                        }

                        // 如果还有重试次数，继续尝试
                        if (retryCount < maxRetries - 1) {
                            retryCount++;
                            logger.log(`删除邮件失败，正在重试 (${retryCount}/${maxRetries})...`, 'warning');
                            setTimeout(tryDelete, 500);
                        } else {
                            logger.log("删除邮件失败，已达到最大重试次数", 'error');
                            resolve(false);
                        }
                    },
                    onerror: function(error) {
                        if (retryCount < maxRetries - 1) {
                            retryCount++;
                            logger.log(`删除邮件出错，正在重试 (${retryCount}/${maxRetries})...`, 'warning');
                            setTimeout(tryDelete, 500);
                        } else {
                            logger.log("删除邮件失败: " + error, 'error');
                            resolve(false);
                        }
                    }
                });
            }

            tryDelete();
        });
    }

    // 获取最新邮件中的验证码
    async function getLatestMailCode() {
        return new Promise((resolve, reject) => {
            const mailListUrl = `https://tempmail.plus/api/mails?email=${TEMP_MAIL_CONFIG.username}${TEMP_MAIL_CONFIG.emailExtension}&limit=20&epin=${TEMP_MAIL_CONFIG.epin}`;

            GM_xmlhttpRequest({
                method: "GET",
                url: mailListUrl,
                onload: async function(mailListResponse) {
                    try {
                        const mailListData = JSON.parse(mailListResponse.responseText);
                        if (!mailListData.result || !mailListData.first_id) {
                            resolve(null);
                            return;
                        }

                        const firstId = mailListData.first_id;
                        const mailDetailUrl = `https://tempmail.plus/api/mails/${firstId}?email=${TEMP_MAIL_CONFIG.username}${TEMP_MAIL_CONFIG.emailExtension}&epin=${TEMP_MAIL_CONFIG.epin}`;

                        GM_xmlhttpRequest({
                            method: "GET",
                            url: mailDetailUrl,
                            onload: async function(mailDetailResponse) {
                                try {
                                    const mailDetailData = JSON.parse(mailDetailResponse.responseText);
                                    if (!mailDetailData.result) {
                                        resolve(null);
                                        return;
                                    }

                                    const mailText = mailDetailData.text || "";
                                    const mailSubject = mailDetailData.subject || "";
                                    logger.log("找到邮件主题: " + mailSubject);

                                    const code = extractVerificationCode(mailText);

                                    // 获取到验证码后，尝试删除邮件
                                    if (code) {
                                        await deleteEmail(firstId);
                                    }

                                    resolve(code);
                                } catch (error) {
                                    logger.log("解析邮件详情失败: " + error, 'error');
                                    resolve(null);
                                }
                            },
                            onerror: function(error) {
                                logger.log("获取邮件详情失败: " + error, 'error');
                                resolve(null);
                            }
                        });
                    } catch (error) {
                        logger.log("解析邮件列表失败: " + error, 'error');
                        resolve(null);
                    }
                },
                onerror: function(error) {
                    logger.log("获取邮件列表失败: " + error, 'error');
                    resolve(null);
                }
            });
        });
    }

    // 获取验证码（带重试机制）
    async function getVerificationCode(maxRetries = 5, retryInterval = 3000) {
        for (let attempt = 0; attempt < maxRetries; attempt++) {
            logger.log(`尝试获取验证码 (第 ${attempt + 1}/${maxRetries} 次)...`);

            try {
                const code = await getLatestMailCode();
                if (code) {
                    logger.log("成功获取验证码: " + code, 'success');
                    return code;
                }

                if (attempt < maxRetries - 1) {
                    logger.log(`未获取到验证码，${retryInterval/1000}秒后重试...`, 'warning');
                    await new Promise(resolve => setTimeout(resolve, retryInterval));
                }
            } catch (error) {
                logger.log("获取验证码出错: " + error, 'error');
                if (attempt < maxRetries - 1) {
                    await new Promise(resolve => setTimeout(resolve, retryInterval));
                }
            }
        }

        throw new Error(`经过 ${maxRetries} 次尝试后仍未获取到验证码。`);
    }

    // 自动填写邮箱并提交
    async function fillEmail() {
        const email = generateEmail();
        logger.log('使用邮箱: ' + email);
        GM_setValue('registered_email', email); // 保存邮箱以便后续使用

        const emailInput = await waitForElement('input[name="username"]');
        if (!emailInput) {
            logger.log('未找到邮箱输入框', 'error');
            return false;
        }

        logger.log('找到邮箱输入框，开始填写');

        // 填写邮箱
        emailInput.value = email;
        emailInput.dispatchEvent(new Event('input', { bubbles: true }));

        // 点击继续按钮
        const continueBtn = await waitForElement('button[type="submit"]');
        if (!continueBtn) {
            logger.log('未找到继续按钮', 'error');
            return false;
        }

        continueBtn.click();
        return true;
    }

    // 填写验证码
    async function fillVerificationCode() {
        const code = await getVerificationCode();
        if (!code) {
            logger.log('未能获取验证码', 'error');
            return false;
        }

        const codeInput = await waitForElement('input[name="code"]');
        if (!codeInput) {
            logger.log('未找到验证码输入框', 'error');
            return false;
        }

        // 填写验证码
        codeInput.value = code;
        codeInput.dispatchEvent(new Event('input', { bubbles: true }));

        // 点击继续按钮
        const continueBtn = await waitForElement('button[type="submit"]');
        if (!continueBtn) {
            logger.log('未找到继续按钮', 'error');
            return false;
        }

        continueBtn.click();
        return true;
    }

    // 同意服务条款并完成注册
    async function completeRegistration() {
        const checkbox = await waitForElement('input[type="checkbox"]');
        if (checkbox) {
            checkbox.click();
        }

        const signupBtn = await waitForElementWithText('button', 'Sign up');
        if (!signupBtn) {
            logger.log('未找到注册按钮', 'error');
            return false;
        }

        signupBtn.click();
        return true;
    }

    // 复制到剪贴板
    function copyToClipboard(text) {
        GM_setClipboard(text, 'text');
        logger.log(`已复制到剪贴板: ${text}`, 'success');
    }

    // 等待包含特定文本的元素
    async function waitForElementWithText(selector, text, timeout = 10000, doc = document) {
        const startTime = Date.now();
        while (Date.now() - startTime < timeout) {
            const elements = doc.querySelectorAll(selector);
            for (const element of elements) {
                const elementText = element.textContent.replace(/\s+/g, ' ').trim();
                const normalizedText = text.replace(/\s+/g, ' ').trim();
                if (elementText.includes(normalizedText)) {
                    return element;
                }
            }
            await new Promise(resolve => setTimeout(resolve, 100));
        }
        return null;
    }

    async function handleWindsurfPage() {
        const getCreditsBtn = await waitForElementWithText('a', 'Get free credits');
        if (getCreditsBtn) {
            logger.log('找到 "Get free credits" 按钮，正在点击...');
            getCreditsBtn.click();
        } else {
            logger.log('未找到 "Get free credits" 按钮', 'error');
        }
    }

    // 增强的Shadow DOM搜索函数
    function findInShadowDom(selector, text, searchRoots = [document.body, document.head, document.documentElement]) {
        const find = (root, depth = 0) => {
            if (depth > 10) return null; // 防止无限递归

            try {
                // 在当前根节点中搜索
                const elements = root.querySelectorAll(selector);
                for (const element of elements) {
                    const elementText = element.textContent || element.innerText || '';
                    if (elementText.includes(text)) {
                        return element;
                    }
                }

                // 搜索Shadow DOM
                const allElements = root.querySelectorAll('*');
                for (const host of allElements) {
                    if (host.shadowRoot) {
                        const found = find(host.shadowRoot, depth + 1);
                        if (found) return found;
                    }
                }
            } catch (error) {
                logger.log(`Shadow DOM搜索出错 (深度${depth}): ${error.message}`, 'warning');
            }

            return null;
        };

        // 在多个根节点中搜索
        for (const root of searchRoots) {
            if (root) {
                const result = find(root);
                if (result) return result;
            }
        }

        return null;
    }

    async function handleHumanVerification() {
        logger.log('开始人机验证处理...', 'info');

        // 定义多重定位策略
        const verificationStrategies = [
            {
                name: '精确CSS选择器策略',
                find: async () => {
                    // 查找包含验证文本的容器，然后在其中找复选框
                    const containers = document.querySelectorAll('div, form, section, fieldset');
                    for (const container of containers) {
                        const text = container.textContent || '';
                        if (text.includes('Verify you are human') || text.includes('verify you are human')) {
                            const checkbox = container.querySelector('input[type="checkbox"]:not([disabled])');
                            if (checkbox) {
                                logger.log('通过CSS选择器找到验证复选框');
                                return checkbox;
                            }
                        }
                    }
                    return null;
                }
            },
            {
                name: '文本邻近定位策略',
                find: async () => {
                    // 查找包含验证文本的元素，然后查找附近的复选框
                    const textElements = document.querySelectorAll('*');
                    for (const element of textElements) {
                        const text = element.textContent || '';
                        if (text.includes('Verify you are human') && element.children.length < 10) {
                            // 在父容器中查找复选框
                            const parent = element.closest('div, form, section') || element.parentElement;
                            if (parent) {
                                const checkbox = parent.querySelector('input[type="checkbox"]:not([disabled])');
                                if (checkbox) {
                                    logger.log('通过文本邻近定位找到验证复选框');
                                    return checkbox;
                                }
                            }
                        }
                    }
                    return null;
                }
            },
            {
                name: 'Shadow DOM深度搜索策略',
                find: async () => {
                    const textElement = findInShadowDom('*', 'Verify you are human');
                    if (textElement) {
                        const root = textElement.getRootNode();
                        const checkbox = root.querySelector('input[type="checkbox"]:not([disabled])');
                        if (checkbox) {
                            logger.log('通过Shadow DOM搜索找到验证复选框');
                            return checkbox;
                        }
                    }
                    return null;
                }
            },
            {
                name: '属性组合匹配策略',
                find: async () => {
                    // 查找可能的验证复选框（通过属性组合）
                    const checkboxes = document.querySelectorAll('input[type="checkbox"]:not([disabled])');
                    for (const checkbox of checkboxes) {
                        const parent = checkbox.closest('div, form, section');
                        if (parent) {
                            const parentText = parent.textContent || '';
                            if (parentText.includes('human') || parentText.includes('verify') ||
                                parentText.includes('robot') || parentText.includes('captcha')) {
                                logger.log('通过属性组合匹配找到可能的验证复选框');
                                return checkbox;
                            }
                        }
                    }
                    return null;
                }
            }
        ];

        try {
            // 使用智能等待函数尝试所有策略
            const result = await waitForElementAdvanced(verificationStrategies, 20000);

            if (!result) {
                throw new Error('所有定位策略均未找到人机验证复选框');
            }

            const { element: checkbox, strategy } = result;

            // 检查复选框状态
            if (checkbox.checked) {
                logger.log('验证复选框已经被选中', 'success');
                return true;
            }

            // 点击复选框
            logger.log(`使用策略"${strategy}"找到复选框，正在点击...`);
            checkbox.click();

            // 等待验证成功的多种检查方式
            logger.log('等待验证完成...');
            const verificationSuccess = await waitForVerificationSuccess();

            if (verificationSuccess) {
                logger.log('人机验证成功完成！', 'success');
                return true;
            } else {
                throw new Error('点击复选框后验证未成功');
            }

        } catch (error) {
            logger.log(`人机验证处理失败: ${error.message}`, 'error');
            throw error;
        }
    }

    // 等待验证成功的函数
    async function waitForVerificationSuccess(timeout = 15000) {
        const startTime = Date.now();

        while (Date.now() - startTime < timeout) {
            // 检查成功标识的多种方式
            const successIndicators = [
                () => document.querySelector('*')?.textContent?.includes('Success'),
                () => document.querySelector('*')?.textContent?.includes('Verified'),
                () => document.querySelector('input[type="checkbox"]:checked'),
                () => !document.querySelector('*')?.textContent?.includes('Verify you are human'),
                () => document.querySelector('.success, .verified, .complete')
            ];

            for (const check of successIndicators) {
                try {
                    if (check()) {
                        logger.log('检测到验证成功标识');
                        return true;
                    }
                } catch (e) {
                    // 忽略检查错误，继续下一个
                }
            }

            await new Promise(resolve => setTimeout(resolve, 500));
        }

        return false;
    }

    async function handleSubscriptionPage() {
        if (document.body.innerText.includes('650 available')) {
            logger.log('已成功切换到Community Plan。', 'success');
            const email = await GM_getValue('registered_email', '');
            if (email) {
                copyToClipboard(email);
            }
            return;
        }

        logger.log('开始切换订阅计划...');
        const changePlanBtn = await waitForElementWithText('button', 'Change plan');
        if (!changePlanBtn) {
            logger.log('未找到 "Change plan" 按钮', 'error');
            return;
        }
        changePlanBtn.click();
        logger.log('点击 "Change plan" 按钮');

        const communityPlanBtn = await waitForElementWithText('h3', 'Community Plan', 5000);
        if (!communityPlanBtn) {
            logger.log('未找到 "Community Plan"', 'error');
            return;
        }
        communityPlanBtn.parentElement.click();
        logger.log('选择 "Community Plan"');

        const confirmCheckbox = await waitForElement('input[type="checkbox"]', 5000);
        if (!confirmCheckbox) {
            logger.log('未找到确认复选框', 'error');
            return;
        }
        if (!confirmCheckbox.checked) {
            confirmCheckbox.click();
            logger.log('勾选确认框');
        }

        const selectPlanBtn = await waitForElementWithText('button', 'Select Plan');
        if (!selectPlanBtn) {
            logger.log('未找到 "Select Plan" 按钮', 'error');
            return;
        }
        selectPlanBtn.click();
        logger.log('点击 "Select Plan" 按钮');

        logger.log('等待计划切换成功...');
        const successIndicator = await waitForElementWithText('div', '650 available', 20000);
        if (successIndicator) {
            logger.log('成功切换到Community Plan！', 'success');
            const email = await GM_getValue('registered_email', '');
            if (email) {
                copyToClipboard(email);
            }
        } else {
            logger.log('切换计划失败或超时。', 'error');
        }
    }

    async function handleLoginPage() {
        const emailInput = document.querySelector('input[name="username"]');
        const codeInput = document.querySelector('input[name="code"]');
        const termsCheckbox = document.querySelector('input[type="checkbox"]'); // More generic selector
        const isFailed = document.body.innerText.includes('Sign up failed');

        if (isFailed) {
            logger.log('注册失败！服务器拒绝了请求。这通常是因为临时邮箱域名被屏蔽或IP地址被标记。', 'error');
            logger.log('建议：请尝试在 tempmail.plus 网站更换一个新的邮箱域名，然后修改脚本中的 EMAIL_DOMAIN。', 'warning');
        } else if (emailInput) {
            logger.log('检测到邮箱输入页面');

            // 先显示注册按钮，让用户可以手动触发
            const registerButton = logger.showRegisterButton();
            if (registerButton) {
                registerButton.addEventListener('click', async () => {
                    try {
                        registerButton.disabled = true;
                        registerButton.textContent = '正在验证...';

                        // 先处理人机验证
                        logger.log('开始处理人机验证...');
                        await handleHumanVerification();

                        // 验证成功后填写邮箱
                        registerButton.textContent = '正在填写...';
                        if (await fillEmail()) {
                            logger.log('邮箱填写完成，请等待页面跳转...', 'success');
                        }
                    } catch (error) {
                        logger.log('注册过程出错: ' + error, 'error');
                        registerButton.disabled = false;
                        registerButton.textContent = '重试';
                    }
                });
            }

            // 自动尝试处理人机验证（不阻塞用户操作）
            setTimeout(async () => {
                try {
                    logger.log('自动尝试处理人机验证...');
                    await handleHumanVerification();
                    logger.log('自动人机验证完成，可以点击注册按钮', 'success');
                } catch (error) {
                    logger.log('自动人机验证失败，请手动点击注册按钮: ' + error.message, 'warning');
                }
            }, 2000); // 延迟2秒让页面完全加载
        } else if (codeInput) {
            logger.log('检测到验证码输入页面，自动执行...');
            try {
                if (await fillVerificationCode()) {
                    logger.log('验证码填写完成，继续注册...', 'success');
                }
            } catch (error) {
                logger.log('填写验证码过程出错: ' + error, 'error');
            }
        } else if (termsCheckbox && document.body.innerText.includes('Terms of Service')) {
            logger.log('检测到服务条款页面，自动完成...');
            try {
                if (await completeRegistration()) {
                    logger.log('注册流程完成！等待跳转...', 'success');
                }
            } catch (error) {
                logger.log('完成注册过程出错: ' + error, 'error');
            }
        } else {
            logger.log('无法识别当前登录页面状态', 'warning');
        }
    }

    // 主函数
    async function main() {
        const currentUrl = window.location.href;
        logger.log(`当前URL: ${currentUrl}`);

        if (currentUrl.includes('/resources/windsurf')) {
            logger.log('检测到Windsurf页面');
            await handleWindsurfPage();
        } else if (currentUrl.includes('login.augmentcode.com') || currentUrl.includes('auth.augmentcode.com')) {
            logger.log('检测到注册/登录页面');
            await handleLoginPage();
        } else if (currentUrl.includes('/promotions/windsurf')) {
            logger.log('检测到文件上传页面，请手动上传PDF文件以继续。', 'warning');
            // The script will wait for navigation to the subscription page.
        } else if (currentUrl.includes('/account/subscription')) {
            logger.log('检测到Subscription页面');
            await handleSubscriptionPage();
        } else {
            logger.log('当前页面不是脚本目标页面', 'info');
        }
    }

    // 启动脚本
    main().catch(error => logger.log('脚本启动出错: ' + error, 'error'));
})();
